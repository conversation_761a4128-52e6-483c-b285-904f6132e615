"""
# 连续同步 AI/AO 模块

模块路径：`sweeper400.measure.cont_sync_io`

包含同步的连续 AI 和 AO 任务实现，主要用于单频正弦波信号的生成和采集。
"""

import threading
import time
from typing import Callable, Optional
import numpy as np

import nidaqmx
from nidaqmx.constants import AcquisitionType, RegenerationMode, Edge

from sweeper400.analyze import (
    Waveform,
    SineArgs,
    PositiveInt,
    WaveformGenerator,
)
from sweeper400.logger import get_logger

# 获取模块日志器
logger = get_logger(__name__)


class ContSyncSineAIAO:
    """
    # 连续同步正弦波 AI/AO 类

    该类创建同步的单通道持续 AI 和 AO 任务，专门用于单频正弦波信号的生成和采集。
    使用 PXIe_CLK100 参考时钟和硬件触发器确保严格同步。

    ## 主要特性：
    - 硬件同步的连续 AI/AO 任务
    - 基于回调函数的数据处理
    - 动态波形生成（非再生模式）
    - 线程安全的数据传输控制
    - 自动资源管理

    ## 使用示例：
    ```python
    from sweeper400.analyze import init_sampling_info, init_sine_args, SineGenerator
    from sweeper400.measure.cont_sync_io import ContSyncSineAIAO

    # 创建采样信息和波形生成器
    sampling_info = init_sampling_info(1000, 1000)
    sine_args = init_sine_args(100.0, 1.0, 0.0)
    generator = SineGenerator(sampling_info, sine_args)

    # 定义数据导出函数
    def export_data(ai_waveform, ao_waveform, ao_sine_args, chunks_num):
        print(f"导出第 {chunks_num} 段数据")

    # 创建同步 AI/AO 对象
    sync_io = ContSyncSineAIAO(
        ai_channel="PXI1Slot2/ai0",
        ao_channel="PXI1Slot2/ao0",
        waveform_generator=generator,
        export_function=export_data
    )

    # 启动任务
    sync_io.start()
    sync_io.enable_export = True  # 开始导出数据

    # 运行一段时间后停止
    time.sleep(10)
    sync_io.stop()
    ```
    """

    def __init__(
        self,
        ai_channel: str,
        ao_channel: str,
        waveform_generator: WaveformGenerator,
        export_function: Callable[[Waveform, Waveform, SineArgs, PositiveInt], None],
    ) -> None:
        """
        初始化连续同步正弦波 AI/AO 对象

        Args:
            ai_channel: AI 通道名称，例如 "PXI1Slot2/ai0"
            ao_channel: AO 通道名称，例如 "PXI1Slot2/ao0"
            waveform_generator: 波形生成器，用于生成连续的正弦波形
            export_function: 数据导出函数，接收 (ai_waveform, ao_waveform, ao_sine_args, chunks_num) 参数

        Raises:
            ValueError: 当参数无效时
        """
        logger.info(f"初始化 ContSyncSineAIAO - AI: {ai_channel}, AO: {ao_channel}")

        # 公有属性
        self.enable_export: bool = False
        self.export_function = export_function

        # 私有属性 - 基本配置
        self._ai_channel = ai_channel
        self._ao_channel = ao_channel
        self._waveform_generator = waveform_generator
        self._exported_chunks: int = 0

        # 私有属性 - 任务和状态管理
        self._ai_task: Optional[nidaqmx.Task] = None
        self._ao_task: Optional[nidaqmx.Task] = None
        self._is_running = False
        self._callback_lock = threading.Lock()
        self._worker_thread: Optional[threading.Thread] = None
        self._stop_event = threading.Event()
        self._data_queue: list = []  # 用于在回调和工作线程间传递数据

        # 获取采样信息
        self._sampling_info = waveform_generator._sampling_info

        # 初始化波形数据
        logger.debug("生成初始波形数据")

        # 保存当前的正弦波参数作为 previous
        self._previous_sine_args = waveform_generator._next_sine_args.copy()

        # 生成第一段波形（previous）
        self._previous_waveform = waveform_generator.generate()

        # 生成第二段波形（next）
        self._next_waveform = waveform_generator.generate()

        # 计算时长（纳秒）
        self._duration_ns = int(self._next_waveform.duration * 1e9)

        logger.debug(
            f"初始化完成 - 采样率: {self._sampling_info['sampling_rate']} Hz, "
            f"每段样本数: {self._sampling_info['samples_num']}, "
            f"段时长: {self._duration_ns} ns"
        )

    def _callback_function(
        self, task_handle, every_n_samples_event_type, number_of_samples, callback_data
    ):
        """
        AI 任务回调函数

        当缓冲区中的采集数据积累到指定数量时自动调用。
        由于在硬件中断线程中执行，需要快速处理并保证线程安全。
        （未使用的参数不可删除，其为nidaqmx包的API要求）

        Args:
            task_handle: 任务句柄
            every_n_samples_event_type: 事件类型
            number_of_samples: 样本数量
            callback_data: 回调数据

        Returns:
            int: 0 表示成功
        """
        try:
            # 快速读取数据并加入队列，避免在回调中执行耗时操作
            with self._callback_lock:
                if self._ai_task is not None:
                    # 读取 AI 数据
                    ai_data = self._ai_task.read(
                        number_of_samples_per_channel=number_of_samples
                    )

                    # 准备数据包
                    data_package = {
                        "ai_data": ai_data,
                        "timestamp": self._previous_waveform.timestamp,
                        "ao_waveform": self._previous_waveform.copy(),
                        "ao_sine_args": self._previous_sine_args.copy(),
                        "enable_export": self.enable_export,
                    }

                    # 加入处理队列
                    self._data_queue.append(data_package)

                    # 更新波形数据（为下一次做准备）
                    self._update_waveforms_for_next_cycle()

            return 0

        except Exception as e:
            logger.error(f"回调函数执行失败: {e}")
            return -1

    def _update_waveforms_for_next_cycle(self):
        """
        为下一次波形输出做准备（在回调函数中调用）

        该方法在回调函数的锁保护下执行，快速更新波形数据。
        """
        try:
            # 更新 previous 参数和波形
            self._previous_sine_args = self._waveform_generator._next_sine_args.copy()
            self._previous_waveform = self._next_waveform.copy()

            # 生成新的 next 波形
            self._next_waveform = self._waveform_generator.generate()

            # 将新输出波形直接写入 AO 缓冲区（如果有足够空间）
            # 这样可以避免在回调函数中执行可能阻塞的写入操作
            if self._ao_task is not None and self._is_running:
                # 检查缓冲区空间
                try:
                    space_available = self._ao_task.out_stream.space_avail
                    if space_available >= len(self._next_waveform):
                        # 有足够空间，直接写入
                        self._ao_task.write(self._next_waveform, auto_start=False)
                    else:
                        # 空间不足，记录警告但不阻塞
                        logger.debug(
                            f"AO 缓冲区空间不足: {space_available} < {len(self._next_waveform)}"
                        )
                except Exception as write_e:
                    # 写入失败，记录错误但不影响回调函数执行
                    logger.debug(f"AO 写入失败: {write_e}")

        except Exception as e:
            logger.error(f"波形更新失败: {e}")

    def _worker_thread_function(self):
        """
        工作线程函数

        处理从回调函数队列中获取的数据，执行数据导出等耗时操作。
        """
        logger.debug("工作线程已启动")

        while not self._stop_event.is_set():
            try:
                # 检查是否有待处理的数据
                data_to_process = []
                with self._callback_lock:
                    if self._data_queue:
                        data_to_process = self._data_queue.copy()
                        self._data_queue.clear()

                # 处理数据
                for data_package in data_to_process:
                    self._process_data_package(data_package)

                # 短暂休眠避免过度占用 CPU
                time.sleep(0.001)  # 1ms

            except Exception as e:
                logger.error(f"工作线程处理数据失败: {e}")

        logger.debug("工作线程已退出")

    def _process_data_package(self, data_package: dict):
        """
        处理单个数据包

        Args:
            data_package: 包含 AI 数据和相关信息的数据包
        """
        try:
            ai_data = data_package["ai_data"]
            timestamp = data_package["timestamp"]
            ao_waveform = data_package["ao_waveform"]
            ao_sine_args = data_package["ao_sine_args"]
            enable_export = data_package["enable_export"]

            if enable_export:
                # 增加导出计数
                self._exported_chunks += 1

                # 创建 AI 波形对象
                ai_waveform = Waveform(
                    np.array(ai_data),
                    sampling_rate=self._sampling_info["sampling_rate"],
                    timestamp=timestamp,
                )

                # 调用导出函数
                self.export_function(
                    ai_waveform, ao_waveform, ao_sine_args, self._exported_chunks
                )

                logger.debug(f"导出第 {self._exported_chunks} 段数据")

            else:
                # 重置导出计数
                self._exported_chunks = 0

        except Exception as e:
            logger.error(f"数据包处理失败: {e}")

    def start(self):
        """
        启动同步的连续 AI/AO 任务

        配置并启动硬件同步的 AI 和 AO 任务，使用 PXIe_CLK100 时钟源和触发器同步。

        Raises:
            RuntimeError: 当任务启动失败时
        """
        if self._is_running:
            logger.warning("任务已在运行中")
            return

        logger.info("启动同步 AI/AO 任务")

        try:
            # 创建 AI 和 AO 任务
            self._ai_task = nidaqmx.Task("ContSyncAI")
            self._ao_task = nidaqmx.Task("ContSyncAO")

            # 配置 AI 任务
            self._setup_ai_task()

            # 配置 AO 任务
            self._setup_ao_task()

            # 配置硬件同步触发
            self._setup_hardware_sync()

            # 启动工作线程
            self._stop_event.clear()
            self._worker_thread = threading.Thread(
                target=self._worker_thread_function, name="ContSyncSineAIAO_Worker"
            )
            self._worker_thread.start()

            # 启动任务（AO 先启动，等待触发）
            self._ao_task.start()
            self._ai_task.start()  # AI 启动时会触发 AO

            self._is_running = True
            logger.info("同步 AI/AO 任务启动成功")

        except Exception as e:
            logger.error(f"任务启动失败: {e}")
            self._cleanup_tasks()
            raise RuntimeError(f"同步 AI/AO 任务启动失败: {e}")

    def _setup_ai_task(self):
        """配置 AI 任务"""
        if self._ai_task is None:
            raise RuntimeError("AI 任务未创建")

        logger.debug("配置 AI 任务")

        # 添加 AI 通道
        self._ai_task.ai_channels.add_ai_voltage_chan(
            self._ai_channel, min_val=-10.0, max_val=10.0
        )

        # 配置时钟源和采样
        self._ai_task.timing.ref_clk_src = "PXIe_Clk100"
        self._ai_task.timing.ref_clk_rate = 100000000
        self._ai_task.timing.cfg_samp_clk_timing(
            rate=self._sampling_info["sampling_rate"],
            sample_mode=AcquisitionType.CONTINUOUS,
        )

        # 注册回调函数
        self._ai_task.register_every_n_samples_acquired_into_buffer_event(
            self._sampling_info["samples_num"], self._callback_function
        )

        logger.debug("AI 任务配置完成")

    def _setup_ao_task(self):
        """配置 AO 任务"""
        if self._ao_task is None:
            raise RuntimeError("AO 任务未创建")

        logger.debug("配置 AO 任务")

        # 添加 AO 通道
        self._ao_task.ao_channels.add_ao_voltage_chan(
            self._ao_channel, min_val=-10.0, max_val=10.0
        )

        # 配置时钟源和采样
        self._ao_task.timing.ref_clk_src = "PXIe_Clk100"
        self._ao_task.timing.ref_clk_rate = 100000000
        self._ao_task.timing.cfg_samp_clk_timing(
            rate=self._sampling_info["sampling_rate"],
            sample_mode=AcquisitionType.CONTINUOUS,
        )

        # 设置非再生模式
        self._ao_task.out_stream.regen_mode = RegenerationMode.DONT_ALLOW_REGENERATION

        # 配置更大的输出缓冲区以避免下溢
        buffer_size = self._sampling_info["samples_num"] * 10  # 10倍缓冲区
        self._ao_task.out_stream.output_buf_size = buffer_size
        logger.debug(f"设置 AO 缓冲区大小: {buffer_size} 样本")

        # 写入初始波形数据（写入多个周期以填充缓冲区）
        initial_data = np.tile(self._next_waveform, 3)  # 重复3次
        self._ao_task.write(initial_data, auto_start=False)
        logger.debug(f"写入初始数据: {len(initial_data)} 样本")

        logger.debug("AO 任务配置完成")

    def _setup_hardware_sync(self):
        """配置硬件同步触发"""
        if self._ai_task is None or self._ao_task is None:
            raise RuntimeError("AI 或 AO 任务未创建")

        logger.debug("配置硬件同步触发")

        # 从通道名称提取设备名称
        device_name = self._ai_channel.split("/")[0]

        # 尝试多种硬件同步方法
        hardware_sync_success = False

        # 方法1: 使用时序引擎 StartTrigger 终端
        te_terminals = [
            f"/{device_name}/te0/StartTrigger",
            f"/{device_name}/te1/StartTrigger",
            f"/{device_name}/te2/StartTrigger",
            f"/{device_name}/te3/StartTrigger",
        ]

        for te_terminal in te_terminals:
            try:
                self._ao_task.triggers.start_trigger.cfg_dig_edge_start_trig(
                    te_terminal, trigger_edge=Edge.RISING
                )
                logger.debug(f"成功配置时序引擎触发: {te_terminal}")
                hardware_sync_success = True
                break

            except Exception as te_e:
                logger.debug(f"时序引擎终端 {te_terminal} 失败: {te_e}")
                continue

        if not hardware_sync_success:
            logger.warning("时序引擎触发方法失败，尝试 AI StartTrigger")

            # 方法2: 使用 AI 任务的 StartTrigger（实际上无效，仅作为备用方法的示例）
            try:
                ai_start_trigger = f"/{device_name}/ai/StartTrigger"
                self._ao_task.triggers.start_trigger.cfg_dig_edge_start_trig(
                    ai_start_trigger, trigger_edge=Edge.RISING
                )
                logger.debug(f"成功配置 AI StartTrigger: {ai_start_trigger}")
                hardware_sync_success = True

            except Exception as ai_e:
                logger.debug(f"AI StartTrigger 失败: {ai_e}")

        if not hardware_sync_success:
            logger.error("所有硬件同步方法都失败")
            raise RuntimeError("硬件触发配置失败: 无法建立 AI/AO 任务间的硬件同步")

        logger.info("硬件同步触发配置成功")

    def stop(self):
        """
        停止同步的连续 AI/AO 任务并释放所有资源

        按照正确的顺序停止任务、清理资源，确保硬件状态正常。
        """
        if not self._is_running:
            logger.warning("任务未在运行中")
            return

        logger.info("停止同步 AI/AO 任务")

        try:
            # 标记停止状态
            self._is_running = False

            # 停止工作线程
            self._stop_worker_thread()

            # 停止并清理 nidaqmx 任务
            self._cleanup_tasks()

            # 重置状态
            self._exported_chunks = 0

            logger.info("同步 AI/AO 任务已停止")

        except Exception as e:
            logger.error(f"任务停止过程中发生错误: {e}")
            # 即使出错也要尝试清理资源
            try:
                self._cleanup_tasks()
            except Exception:
                pass

    def _stop_worker_thread(self):
        """停止工作线程"""
        if self._worker_thread is not None:
            logger.debug("停止工作线程")

            # 设置停止事件
            self._stop_event.set()

            # 等待线程结束
            self._worker_thread.join(timeout=5.0)

            if self._worker_thread.is_alive():
                logger.warning("工作线程未能在超时时间内结束")
            else:
                logger.debug("工作线程已停止")

            self._worker_thread = None

    def _cleanup_tasks(self):
        """清理 nidaqmx 任务资源"""
        logger.debug("清理 nidaqmx 任务资源")

        # 停止 AI 任务
        if self._ai_task is not None:
            try:
                if self._ai_task._handle is not None:
                    self._ai_task.stop()
                    logger.debug("AI 任务已停止")
            except Exception as e:
                logger.warning(f"停止 AI 任务时出错: {e}")

            try:
                self._ai_task.close()
                logger.debug("AI 任务已关闭")
            except Exception as e:
                logger.warning(f"关闭 AI 任务时出错: {e}")

            self._ai_task = None

        # 停止 AO 任务
        if self._ao_task is not None:
            try:
                if self._ao_task._handle is not None:
                    self._ao_task.stop()
                    logger.debug("AO 任务已停止")
            except Exception as e:
                logger.warning(f"停止 AO 任务时出错: {e}")

            try:
                self._ao_task.close()
                logger.debug("AO 任务已关闭")
            except Exception as e:
                logger.warning(f"关闭 AO 任务时出错: {e}")

            self._ao_task = None

        # 清理数据队列
        with self._callback_lock:
            self._data_queue.clear()

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        上下文管理器出口，自动停止任务
        （未使用的参数不可删除，其为Python上下文管理器协议的API要求）
        """
        if self._is_running:
            self.stop()

    def __del__(self):
        """析构函数，确保资源清理"""
        if hasattr(self, "_is_running") and self._is_running:
            try:
                self.stop()
            except Exception:
                pass  # 析构时忽略异常
