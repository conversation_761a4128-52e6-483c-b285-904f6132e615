"""
# ContSyncSineAIAO 演示脚本

演示如何使用 ContSyncSineAIAO 类进行连续同步正弦波 AI/AO 操作。
注意：此演示需要真实的 NI 硬件连接。
"""

import time
import numpy as np
from sweeper400.analyze import init_sampling_info, SineGenerator
from sweeper400.measure import ContSyncSineAIAO
from sweeper400.logger import get_logger, set_debug_mode

# 获取日志器并设置调试模式
logger = get_logger(__name__)
set_debug_mode()


def demo_export_function(ai_waveform, ao_waveform, ao_sine_args, chunks_num):
    """
    演示用的数据导出函数
    
    Args:
        ai_waveform: 采集到的 AI 波形数据
        ao_waveform: 对应的 AO 输出波形数据
        ao_sine_args: AO 输出的正弦波参数
        chunks_num: 当前段数
    """
    # 计算 AI 数据的基本统计信息
    ai_mean = np.mean(ai_waveform)
    ai_std = np.std(ai_waveform)
    ai_max = np.max(ai_waveform)
    ai_min = np.min(ai_waveform)
    
    # 计算 AO 数据的基本统计信息
    ao_mean = np.mean(ao_waveform)
    ao_std = np.std(ao_waveform)
    
    logger.info(
        f"段 {chunks_num:3d} | "
        f"AI: 均值={ai_mean:+6.3f}V, 标准差={ai_std:.3f}V, 范围=[{ai_min:+6.3f}, {ai_max:+6.3f}]V | "
        f"AO: 均值={ao_mean:+6.3f}V, 标准差={ao_std:.3f}V | "
        f"频率={ao_sine_args['frequency']:.1f}Hz, 幅值={ao_sine_args['amplitude']:.2f}V"
    )


def main():
    """主演示函数"""
    logger.info("=== ContSyncSineAIAO 演示开始 ===")
    
    try:
        # 1. 创建采样信息
        sampling_rate = 10000.0  # 10kHz 采样率
        samples_per_chunk = 1000  # 每段 1000 个样本 (0.1秒)
        sampling_info = init_sampling_info(sampling_rate, samples_per_chunk)
        
        logger.info(f"采样配置: {sampling_rate} Hz, 每段 {samples_per_chunk} 样本")
        
        # 2. 创建正弦波参数
        sine_args = {
            "frequency": 1000.0,  # 1kHz 正弦波
            "amplitude": 2.0,     # 2V 幅值
            "phase": 0.0          # 0 相位
        }
        
        logger.info(f"正弦波配置: {sine_args['frequency']} Hz, {sine_args['amplitude']} V")
        
        # 3. 创建波形生成器
        generator = SineGenerator(sampling_info, sine_args)
        
        # 4. 设置通道（根据实际硬件配置修改）
        ai_channel = "PXI1Slot3/ai0"  # 输入通道
        ao_channel = "PXI1Slot3/ao0"  # 输出通道
        
        logger.info(f"通道配置: AI={ai_channel}, AO={ao_channel}")
        
        # 5. 创建 ContSyncSineAIAO 对象
        logger.info("创建 ContSyncSineAIAO 对象...")
        sync_io = ContSyncSineAIAO(
            ai_channel=ai_channel,
            ao_channel=ao_channel,
            waveform_generator=generator,
            export_function=demo_export_function
        )
        
        # 6. 启动任务
        logger.info("启动同步 AI/AO 任务...")
        sync_io.start()
        
        # 7. 运行阶段1：不导出数据，让系统稳定
        logger.info("阶段1: 系统稳定运行（不导出数据）...")
        time.sleep(2.0)
        
        # 8. 运行阶段2：开始导出数据
        logger.info("阶段2: 开始导出数据...")
        sync_io.enable_export = True
        time.sleep(5.0)  # 导出 5 秒的数据
        
        # 9. 运行阶段3：停止导出但继续运行
        logger.info("阶段3: 停止导出数据...")
        sync_io.enable_export = False
        time.sleep(2.0)
        
        # 10. 运行阶段4：再次导出数据
        logger.info("阶段4: 再次开始导出数据...")
        sync_io.enable_export = True
        time.sleep(3.0)
        
        # 11. 停止任务
        logger.info("停止同步 AI/AO 任务...")
        sync_io.stop()
        
        logger.info("=== 演示完成 ===")
        
    except Exception as e:
        logger.error(f"演示过程中发生错误: {e}")
        # 确保清理资源
        try:
            if 'sync_io' in locals():
                sync_io.stop()
        except Exception:
            pass
        raise


def simple_test():
    """简单测试（不需要硬件）"""
    logger.info("=== 简单功能测试（无硬件） ===")
    
    # 创建基本配置
    sampling_info = init_sampling_info(1000.0, 100)
    sine_args = {"frequency": 50.0, "amplitude": 1.0, "phase": 0.0}
    generator = SineGenerator(sampling_info, sine_args)
    
    # 创建对象
    sync_io = ContSyncSineAIAO(
        ai_channel="Dev1/ai0",
        ao_channel="Dev1/ao0",
        waveform_generator=generator,
        export_function=demo_export_function
    )
    
    # 测试基本属性
    logger.info(f"enable_export: {sync_io.enable_export}")
    logger.info(f"AI 通道: {sync_io._ai_channel}")
    logger.info(f"AO 通道: {sync_io._ao_channel}")
    logger.info(f"导出段数: {sync_io._exported_chunks}")
    logger.info(f"运行状态: {sync_io._is_running}")
    
    # 测试波形生成
    logger.info(f"Previous 波形长度: {len(sync_io._previous_waveform)}")
    logger.info(f"Next 波形长度: {len(sync_io._next_waveform)}")
    logger.info(f"Previous 正弦波频率: {sync_io._previous_sine_args['frequency']} Hz")
    
    logger.info("=== 简单测试完成 ===")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--hardware":
        # 运行需要硬件的完整演示
        main()
    else:
        # 运行简单测试
        simple_test()
        logger.info("\n提示: 使用 --hardware 参数运行完整硬件演示")
