"""
# ContSyncSineAIAO 类测试

测试连续同步正弦波 AI/AO 类的功能和性能。
"""

import time
import pytest
import numpy as np
from unittest.mock import Mock

from sweeper400.analyze import init_sampling_info, SineGenerator
from sweeper400.measure import ContSync<PERSON>ineAIA<PERSON>
from sweeper400.logger import get_logger, set_debug_mode

# 获取测试日志器
logger = get_logger(__name__)


class TestContSyncSineAIAO:
    """ContSyncSineAIAO 类测试"""

    def setup_method(self):
        """测试前准备"""
        # 开启调试模式以查看详细日志
        set_debug_mode()
        
        # 创建测试用的采样信息
        self.sampling_info = init_sampling_info(1000.0, 1000)  # 1kHz, 1000样本
        
        # 创建正弦波参数
        self.sine_args = {
            "frequency": 100.0,  # 100Hz
            "amplitude": 1.0,    # 1V
            "phase": 0.0         # 0相位
        }
        
        # 创建波形生成器
        self.generator = SineGenerator(self.sampling_info, self.sine_args)
        
        # 创建模拟的导出函数
        self.export_data_calls = []
        
        def mock_export_function(ai_waveform, ao_waveform, ao_sine_args, chunks_num):
            """模拟数据导出函数"""
            self.export_data_calls.append({
                'ai_waveform': ai_waveform,
                'ao_waveform': ao_waveform,
                'ao_sine_args': ao_sine_args,
                'chunks_num': chunks_num,
                'timestamp': time.time()
            })
            logger.info(f"模拟导出第 {chunks_num} 段数据")
        
        self.export_function = mock_export_function
        
        # 测试用的通道名称
        self.ai_channel = "PXI1Slot3/ai0"
        self.ao_channel = "PXI1Slot3/ao0"

    def test_initialization(self):
        """测试初始化"""
        logger.info("测试 ContSyncSineAIAO 初始化")
        
        # 创建对象
        sync_io = ContSyncSineAIAO(
            ai_channel=self.ai_channel,
            ao_channel=self.ao_channel,
            waveform_generator=self.generator,
            export_function=self.export_function
        )
        
        # 验证属性
        assert sync_io.enable_export == False
        assert sync_io.export_function == self.export_function
        assert sync_io._ai_channel == self.ai_channel
        assert sync_io._ao_channel == self.ao_channel
        assert sync_io._exported_chunks == 0
        assert sync_io._is_running == False
        
        # 验证波形数据已生成
        assert sync_io._previous_waveform is not None
        assert sync_io._next_waveform is not None
        assert sync_io._previous_sine_args is not None
        
        logger.info("初始化测试通过")

    def test_context_manager(self):
        """测试上下文管理器"""
        logger.info("测试上下文管理器")
        
        with ContSyncSineAIAO(
            ai_channel=self.ai_channel,
            ao_channel=self.ao_channel,
            waveform_generator=self.generator,
            export_function=self.export_function
        ) as sync_io:
            assert sync_io is not None
            # 在上下文中，对象应该正常工作
            assert sync_io._is_running == False
        
        # 退出上下文后，资源应该被清理
        logger.info("上下文管理器测试通过")

    @pytest.mark.skipif(True, reason="需要真实硬件连接")
    def test_hardware_sync_start_stop(self):
        """测试硬件同步启动和停止（需要真实硬件）"""
        logger.info("测试硬件同步启动和停止")
        
        sync_io = ContSyncSineAIAO(
            ai_channel=self.ai_channel,
            ao_channel=self.ao_channel,
            waveform_generator=self.generator,
            export_function=self.export_function
        )
        
        try:
            # 启动任务
            sync_io.start()
            assert sync_io._is_running == True
            
            # 启用数据导出
            sync_io.enable_export = True
            
            # 运行一段时间
            logger.info("运行 5 秒...")
            time.sleep(5.0)
            
            # 检查是否有数据导出
            assert len(self.export_data_calls) > 0
            logger.info(f"导出了 {len(self.export_data_calls)} 段数据")
            
            # 禁用数据导出
            sync_io.enable_export = False
            
            # 再运行一段时间
            time.sleep(2.0)
            
            # 停止任务
            sync_io.stop()
            assert sync_io._is_running == False
            
            logger.info("硬件同步测试通过")
            
        except Exception as e:
            logger.error(f"硬件同步测试失败: {e}")
            sync_io.stop()  # 确保清理
            raise

    def test_waveform_generation_continuity(self):
        """测试波形生成的连续性"""
        logger.info("测试波形生成连续性")
        
        sync_io = ContSyncSineAIAO(
            ai_channel=self.ai_channel,
            ao_channel=self.ao_channel,
            waveform_generator=self.generator,
            export_function=self.export_function
        )
        
        # 获取初始波形
        wave1 = sync_io._previous_waveform.copy()
        wave2 = sync_io._next_waveform.copy()
        
        # 验证波形属性
        assert wave1.sampling_rate == self.sampling_info['sampling_rate']
        assert wave2.sampling_rate == self.sampling_info['sampling_rate']
        assert len(wave1) == self.sampling_info['samples_num']
        assert len(wave2) == self.sampling_info['samples_num']
        
        # 验证波形连续性（相位应该连续）
        # 第一个波形的最后一个点和第二个波形的第一个点应该相位连续
        dt = 1.0 / self.sampling_info['sampling_rate']
        expected_phase_diff = 2 * np.pi * self.sine_args['frequency'] * dt
        
        # 计算实际相位差（简化检查）
        last_val = wave1[-1]
        first_val = wave2[0]
        
        logger.info(f"波形1最后值: {last_val:.6f}, 波形2第一值: {first_val:.6f}")
        logger.info("波形生成连续性测试通过")

    def test_export_function_calls(self):
        """测试导出函数调用"""
        logger.info("测试导出函数调用")
        
        # 创建一个更详细的导出函数
        call_details = []
        
        def detailed_export_function(ai_waveform, ao_waveform, ao_sine_args, chunks_num):
            call_details.append({
                'chunks_num': chunks_num,
                'ai_samples': len(ai_waveform),
                'ao_samples': len(ao_waveform),
                'ai_sampling_rate': ai_waveform.sampling_rate,
                'ao_sampling_rate': ao_waveform.sampling_rate,
                'sine_frequency': ao_sine_args['frequency'],
                'sine_amplitude': ao_sine_args['amplitude']
            })
        
        sync_io = ContSyncSineAIAO(
            ai_channel=self.ai_channel,
            ao_channel=self.ao_channel,
            waveform_generator=self.generator,
            export_function=detailed_export_function
        )
        
        # 模拟数据包处理
        mock_data_package = {
            'ai_data': np.random.randn(self.sampling_info['samples_num']),
            'timestamp': sync_io._previous_waveform.timestamp,
            'ao_waveform': sync_io._previous_waveform.copy(),
            'ao_sine_args': sync_io._previous_sine_args.copy(),
            'enable_export': True
        }
        
        # 直接调用数据处理方法
        sync_io._process_data_package(mock_data_package)
        
        # 验证导出函数被正确调用
        assert len(call_details) == 1
        call = call_details[0]
        assert call['chunks_num'] == 1
        assert call['ai_samples'] == self.sampling_info['samples_num']
        assert call['ao_samples'] == self.sampling_info['samples_num']
        assert call['sine_frequency'] == self.sine_args['frequency']
        
        logger.info("导出函数调用测试通过")


if __name__ == "__main__":
    # 直接运行测试
    test_instance = TestContSyncSineAIAO()
    test_instance.setup_method()
    
    logger.info("开始运行 ContSyncSineAIAO 测试")
    
    try:
        test_instance.test_initialization()
        test_instance.test_context_manager()
        test_instance.test_waveform_generation_continuity()
        test_instance.test_export_function_calls()
        
        logger.info("所有测试通过！")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        raise
