"""
测试连续输出功能

测试ContinuousOutput类的基本功能，包括启动、停止和波形生成。
"""

import time
import numpy as np
from sweeper400.analyze import init_sampling_info, SineGenerator
from sweeper400.measure.draft1 import ContinuousOutput
from sweeper400.logger import get_logger

# 获取测试日志器
logger = get_logger(__name__)


def test_continuous_output_basic():
    """测试连续输出的基本功能"""
    logger.info("开始测试连续输出基本功能")
    
    # 创建采样信息 - 较小的参数用于测试
    sampling_info = init_sampling_info(1000, 500)  # 1kHz, 每周期500样本
    logger.info(f"采样信息: {sampling_info}")
    
    # 创建正弦波生成器
    generator = SineGenerator(
        sampling_info=sampling_info,
        frequency=50.0,  # 50Hz正弦波
        amplitude=1.0,   # 1V幅值
        next_phase=0.0
    )
    logger.info("创建正弦波生成器: 50Hz, 1V")
    
    # 创建连续输出对象
    output = ContinuousOutput(
        channel="402Dev2Slot2/ao0",  # 使用第一个AO通道
        sampling_info=sampling_info,
        waveform_generator=generator
    )
    logger.info("创建连续输出对象")
    
    try:
        # 启动输出
        logger.info("启动连续输出...")
        output.start()
        
        # 运行5秒
        logger.info("输出运行中，持续5秒...")
        for i in range(5):
            time.sleep(1)
            stats = output.get_statistics()
            logger.info(
                f"第{i+1}秒 - 总样本: {stats['total_samples']}, "
                f"周期数: {stats['cycle_count']}, "
                f"运行状态: {stats['is_running']}"
            )
        
        # 停止输出
        logger.info("停止连续输出...")
        output.stop()
        
        # 获取最终统计信息
        final_stats = output.get_statistics()
        logger.info("最终统计信息:")
        for key, value in final_stats.items():
            logger.info(f"  {key}: {value}")
        
        logger.info("连续输出基本功能测试完成")
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        # 确保停止输出
        if output.is_running:
            output.stop()
        raise


def test_continuous_output_context_manager():
    """测试连续输出的上下文管理器功能"""
    logger.info("开始测试连续输出上下文管理器功能")
    
    # 创建采样信息
    sampling_info = init_sampling_info(2000, 1000)  # 2kHz, 每周期1000样本
    
    # 创建正弦波生成器 - 不同频率
    generator = SineGenerator(
        sampling_info=sampling_info,
        frequency=100.0,  # 100Hz正弦波
        amplitude=0.5,    # 0.5V幅值
    )
    logger.info("创建正弦波生成器: 100Hz, 0.5V")
    
    try:
        # 使用上下文管理器
        with ContinuousOutput(
            channel="402Dev2Slot2/ao0",
            sampling_info=sampling_info,
            waveform_generator=generator
        ) as output:
            logger.info("使用上下文管理器启动连续输出")
            
            # 启动输出
            output.start()
            
            # 运行3秒
            logger.info("输出运行中，持续3秒...")
            for i in range(3):
                time.sleep(1)
                stats = output.get_statistics()
                logger.info(
                    f"第{i+1}秒 - 周期数: {stats['cycle_count']}, "
                    f"平均输出率: {stats.get('average_output_rate', 0):.1f} Hz"
                )
        
        logger.info("上下文管理器自动停止输出")
        logger.info("连续输出上下文管理器功能测试完成")
        
    except Exception as e:
        logger.error(f"上下文管理器测试过程中发生错误: {e}")
        raise


def test_multiple_frequencies():
    """测试不同频率的连续输出"""
    logger.info("开始测试多种频率的连续输出")
    
    frequencies = [25.0, 75.0, 150.0]  # 测试不同频率
    sampling_info = init_sampling_info(1500, 750)  # 1.5kHz, 每周期750样本
    
    for freq in frequencies:
        logger.info(f"测试频率: {freq} Hz")
        
        # 创建对应频率的生成器
        generator = SineGenerator(
            sampling_info=sampling_info,
            frequency=freq,
            amplitude=0.8,  # 0.8V幅值
        )
        
        with ContinuousOutput(
            channel="402Dev2Slot2/ao0",
            sampling_info=sampling_info,
            waveform_generator=generator
        ) as output:
            
            # 启动并运行2秒
            output.start()
            logger.info(f"输出 {freq} Hz 正弦波，持续2秒...")
            time.sleep(2)
            
            # 获取统计信息
            stats = output.get_statistics()
            logger.info(
                f"{freq} Hz 测试完成 - 周期数: {stats['cycle_count']}, "
                f"总样本: {stats['total_samples']}"
            )
    
    logger.info("多种频率连续输出测试完成")


if __name__ == "__main__":
    logger.info("=" * 60)
    logger.info("开始连续输出功能测试")
    logger.info("=" * 60)
    
    try:
        # 测试1: 基本功能
        test_continuous_output_basic()
        logger.info("-" * 40)
        
        # 测试2: 上下文管理器
        test_continuous_output_context_manager()
        logger.info("-" * 40)
        
        # 测试3: 多种频率
        test_multiple_frequencies()
        
        logger.info("=" * 60)
        logger.info("所有连续输出功能测试完成！")
        logger.info("=" * 60)
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        logger.info("=" * 60)
        logger.error("连续输出功能测试失败！")
        logger.info("=" * 60)
        raise
