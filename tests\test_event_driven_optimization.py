"""
# 事件驱动优化测试

测试事件驱动的工作线程优化是否正常工作。
验证 CPU 资源节省和响应性能。
"""

import time
import threading
import numpy as np
from sweeper400.analyze import init_sampling_info, SineGenerator
from sweeper400.measure import ContSyncSineAIAO
from sweeper400.logger import get_logger

# 获取测试日志器
logger = get_logger(__name__)


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.callback_count = 0
        self.export_count = 0
        self.start_time = time.time()
        self.callback_times = []
        self.export_times = []
        self.lock = threading.Lock()
    
    def record_callback(self):
        """记录回调事件"""
        with self.lock:
            self.callback_count += 1
            self.callback_times.append(time.time() - self.start_time)
    
    def record_export(self):
        """记录导出事件"""
        with self.lock:
            self.export_count += 1
            self.export_times.append(time.time() - self.start_time)
    
    def get_stats(self):
        """获取统计信息"""
        with self.lock:
            duration = time.time() - self.start_time
            callback_rate = self.callback_count / duration if duration > 0 else 0
            export_rate = self.export_count / duration if duration > 0 else 0
            
            return {
                'duration': duration,
                'callback_count': self.callback_count,
                'export_count': self.export_count,
                'callback_rate': callback_rate,
                'export_rate': export_rate,
                'callback_times': self.callback_times.copy(),
                'export_times': self.export_times.copy()
            }


def create_monitoring_export_function(monitor: PerformanceMonitor):
    """创建带监控的导出函数"""
    def export_function(ai_waveform, ao_waveform, ao_sine_args, chunks_num):
        monitor.record_export()
        
        ai_rms = np.sqrt(np.mean(ai_waveform**2))
        ao_rms = np.sqrt(np.mean(ao_waveform**2))
        
        # 每10段显示一次，减少输出
        if chunks_num % 10 == 0:
            logger.info(
                f"段 {chunks_num:3d} | "
                f"AI: {ai_rms:.3f}V | AO: {ao_rms:.3f}V | "
                f"频率: {ao_sine_args['frequency']:.0f}Hz"
            )
    
    return export_function


def test_event_driven_performance():
    """测试事件驱动性能"""
    logger.info("=== 事件驱动性能测试 ===")
    
    # 创建性能监控器
    monitor = PerformanceMonitor()
    
    # 创建测试配置
    sampling_info = init_sampling_info(10000.0, 1000)  # 10kHz, 1000样本/段
    sine_args = {
        "frequency": 1000.0,  # 1kHz
        "amplitude": 2.0,     # 2V
        "phase": 0.0
    }
    
    generator = SineGenerator(sampling_info, sine_args)
    export_function = create_monitoring_export_function(monitor)
    
    # 使用物理连接的通道
    ai_channel = "400Slot2/ai1"
    ao_channel = "400Slot2/ao1"
    
    logger.info("🔧 使用事件驱动的工作线程优化")
    logger.info(f"📊 采样配置: {sampling_info['sampling_rate']} Hz, {sampling_info['samples_num']} 样本/段")
    logger.info(f"🎵 信号配置: {sine_args['frequency']} Hz, {sine_args['amplitude']} V")
    
    sync_io = ContSyncSineAIAO(
        ai_channel=ai_channel,
        ao_channel=ao_channel,
        waveform_generator=generator,
        export_function=export_function
    )
    
    try:
        # 启动任务
        logger.info("🚀 启动事件驱动的同步任务...")
        sync_io.start()
        
        # 稳定期（观察无数据时的行为）
        logger.info("⏳ 稳定期（2秒）- 观察无数据时的工作线程行为...")
        time.sleep(2.0)
        
        # 第一阶段：短时间高频数据采集
        logger.info("📈 第一阶段：高频数据采集（5秒）...")
        sync_io.enable_export = True
        time.sleep(5.0)
        
        # 暂停期（观察停止数据后的行为）
        logger.info("⏸️ 暂停期（2秒）- 观察停止数据后的工作线程行为...")
        sync_io.enable_export = False
        time.sleep(2.0)
        
        # 第二阶段：再次数据采集
        logger.info("📈 第二阶段：再次数据采集（3秒）...")
        sync_io.enable_export = True
        time.sleep(3.0)
        
        # 停止任务
        logger.info("🛑 停止任务...")
        sync_io.stop()
        
        # 分析性能统计
        stats = monitor.get_stats()
        
        logger.info("=" * 60)
        logger.info("📊 事件驱动性能统计")
        logger.info("=" * 60)
        logger.info(f"⏱️ 总运行时间: {stats['duration']:.2f} 秒")
        logger.info(f"📞 回调总次数: {stats['callback_count']}")
        logger.info(f"📤 导出总次数: {stats['export_count']}")
        logger.info(f"📈 回调频率: {stats['callback_rate']:.2f} Hz")
        logger.info(f"📈 导出频率: {stats['export_rate']:.2f} Hz")
        
        # 分析回调间隔
        if len(stats['callback_times']) > 1:
            intervals = np.diff(stats['callback_times'])
            logger.info(f"⏱️ 回调间隔统计:")
            logger.info(f"   平均间隔: {np.mean(intervals):.3f} 秒")
            logger.info(f"   标准差: {np.std(intervals):.3f} 秒")
            logger.info(f"   最小间隔: {np.min(intervals):.3f} 秒")
            logger.info(f"   最大间隔: {np.max(intervals):.3f} 秒")
        
        # 评估优化效果
        expected_callback_rate = sampling_info['sampling_rate'] / sampling_info['samples_num']
        logger.info(f"🎯 理论回调频率: {expected_callback_rate:.2f} Hz")
        
        if abs(stats['callback_rate'] - expected_callback_rate) < 0.5:
            logger.info("✅ 事件驱动优化工作正常")
        else:
            logger.warning("⚠️ 回调频率异常，需要检查")
        
        logger.info("=" * 60)
        
        return stats
        
    except Exception as e:
        logger.error(f"事件驱动性能测试失败: {e}")
        sync_io.stop()
        raise


def test_responsiveness():
    """测试响应性"""
    logger.info("=== 响应性测试 ===")
    
    # 创建测试配置
    sampling_info = init_sampling_info(5000.0, 500)  # 5kHz, 500样本/段 (0.1秒)
    sine_args = {
        "frequency": 800.0,   # 800Hz
        "amplitude": 1.5,     # 1.5V
        "phase": 0.0
    }
    
    generator = SineGenerator(sampling_info, sine_args)
    
    # 记录响应时间
    response_times = []
    
    def responsive_export_function(ai_waveform, ao_waveform, ao_sine_args, chunks_num):
        current_time = time.time()
        response_times.append(current_time)
        
        if chunks_num <= 5:
            logger.info(f"📦 段 {chunks_num} 处理时间: {current_time:.3f}")
    
    ai_channel = "400Slot2/ai1"
    ao_channel = "400Slot2/ao1"
    
    sync_io = ContSyncSineAIAO(
        ai_channel=ai_channel,
        ao_channel=ao_channel,
        waveform_generator=generator,
        export_function=responsive_export_function
    )
    
    try:
        # 启动任务
        sync_io.start()
        time.sleep(1.0)
        
        # 记录开始时间
        start_time = time.time()
        logger.info(f"🚀 开始响应性测试，开始时间: {start_time:.3f}")
        
        # 启用导出
        sync_io.enable_export = True
        
        # 运行短时间
        time.sleep(2.0)
        
        # 停止任务
        sync_io.stop()
        
        # 分析响应性
        if response_times:
            first_response = response_times[0] - start_time
            logger.info(f"⚡ 首次响应时间: {first_response:.3f} 秒")
            
            if len(response_times) > 1:
                intervals = np.diff(response_times)
                logger.info(f"📊 响应间隔统计:")
                logger.info(f"   平均: {np.mean(intervals):.3f} 秒")
                logger.info(f"   标准差: {np.std(intervals):.3f} 秒")
            
            if first_response < 0.2:  # 200ms内响应
                logger.info("✅ 响应性优秀")
            else:
                logger.warning("⚠️ 响应性需要改进")
        
        return response_times
        
    except Exception as e:
        logger.error(f"响应性测试失败: {e}")
        sync_io.stop()
        raise


def main():
    """主测试函数"""
    logger.info("🚀 开始事件驱动优化测试")
    logger.info("⚠️ 请确保 400Slot2/ai1 和 400Slot2/ao1 通道已用信号线连接")
    logger.info("=" * 60)
    
    try:
        # 1. 性能测试
        logger.info("🔧 [1/2] 事件驱动性能测试")
        perf_stats = test_event_driven_performance()
        time.sleep(2.0)
        
        # 2. 响应性测试
        logger.info("🔧 [2/2] 响应性测试")
        response_data = test_responsiveness()
        
        logger.info("🎉 所有事件驱动优化测试完成！")
        
        return perf_stats, response_data
        
    except Exception as e:
        logger.error(f"事件驱动优化测试过程中发生错误: {e}")
        raise


if __name__ == "__main__":
    main()
