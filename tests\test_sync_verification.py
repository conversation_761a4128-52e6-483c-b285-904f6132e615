"""
# 同步验证测试

使用物理连接的 AI/AO 通道验证同步任务的准确性。
通过比较 AI 和 AO 信号的 SineArgs 来验证同步性能。
"""

import time
import numpy as np
from typing import List, Dict, Tuple
from dataclasses import dataclass

from sweeper400.analyze import (
    init_sampling_info,
    SineGenerator,
    extract_single_tone_information_vvi,
    SineArgs,
    Waveform,
)
from sweeper400.measure import ContSyncSineAIAO
from sweeper400.logger import get_logger, set_debug_mode

# 获取测试日志器
logger = get_logger(__name__)
# 不使用 debug 模式，只显示 INFO 及以上级别的日志


@dataclass
class SyncTestResult:
    """同步测试结果数据类"""

    chunk_num: int
    ao_sine_args: SineArgs
    ai_sine_args: SineArgs
    amplitude_ratio: float
    phase_diff: float
    frequency_match: bool


class SyncVerificationTester:
    """同步验证测试器"""

    def __init__(self):
        self.test_results: List[SyncTestResult] = []
        self.start_time = time.time()

    def export_function(
        self,
        ai_waveform: Waveform,
        ao_waveform: Waveform,
        ao_sine_args: SineArgs,
        chunks_num: int,
    ) -> None:
        """
        同步验证的数据导出函数

        Args:
            ai_waveform: 采集到的 AI 波形数据
            ao_waveform: 对应的 AO 输出波形数据
            ao_sine_args: AO 输出的正弦波参数
            chunks_num: 当前段数
        """
        try:
            # 使用 extract_single_tone_information_vvi 分析 AI 信号
            ai_analysis = extract_single_tone_information_vvi(
                ai_waveform, ao_sine_args["frequency"]  # 使用 AO 的频率作为目标频率
            )

            # 提取 AI 信号的 SineArgs
            ai_sine_args = {
                "frequency": ai_analysis["frequency"],
                "amplitude": ai_analysis["amplitude"],
                "phase": ai_analysis["phase"],
            }

            # 计算幅值比和相位差
            amplitude_ratio = ai_sine_args["amplitude"] / ao_sine_args["amplitude"]
            phase_diff = ai_sine_args["phase"] - ao_sine_args["phase"]

            # 将相位差标准化到 [-π, π] 范围
            while phase_diff > np.pi:
                phase_diff -= 2 * np.pi
            while phase_diff < -np.pi:
                phase_diff += 2 * np.pi

            # 检查频率匹配（允许小误差）
            frequency_match = (
                abs(ai_sine_args["frequency"] - ao_sine_args["frequency"]) < 1.0
            )

            # 创建测试结果
            result = SyncTestResult(
                chunk_num=chunks_num,
                ao_sine_args=ao_sine_args,
                ai_sine_args=ai_sine_args,
                amplitude_ratio=amplitude_ratio,
                phase_diff=phase_diff,
                frequency_match=frequency_match,
            )

            self.test_results.append(result)

            # 实时显示结果（每5段显示一次，减少输出）
            if chunks_num % 5 == 0 or chunks_num <= 3:
                logger.info(
                    f"段 {chunks_num:3d} | "
                    f"AO: {ao_sine_args['frequency']:.0f}Hz, {ao_sine_args['amplitude']:.2f}V | "
                    f"AI: {ai_sine_args['frequency']:.0f}Hz, {ai_sine_args['amplitude']:.3f}V | "
                    f"比值: {amplitude_ratio:.4f}, 相位差: {phase_diff:.3f}rad"
                )

        except Exception as e:
            logger.error(f"分析第 {chunks_num} 段数据失败: {e}")

    def analyze_sync_stability(self) -> Dict[str, float]:
        """
        分析同步稳定性

        Returns:
            包含稳定性统计信息的字典
        """
        if not self.test_results:
            return {}

        # 提取数据
        amplitude_ratios = [r.amplitude_ratio for r in self.test_results]
        phase_diffs = [r.phase_diff for r in self.test_results]
        frequency_matches = [r.frequency_match for r in self.test_results]

        # 计算统计信息
        stats = {
            "total_chunks": len(self.test_results),
            "amplitude_ratio_mean": np.mean(amplitude_ratios),
            "amplitude_ratio_std": np.std(amplitude_ratios),
            "phase_diff_mean": np.mean(phase_diffs),
            "phase_diff_std": np.std(phase_diffs),
            "frequency_match_rate": np.mean(frequency_matches),
            "amplitude_ratio_range": np.max(amplitude_ratios)
            - np.min(amplitude_ratios),
            "phase_diff_range": np.max(phase_diffs) - np.min(phase_diffs),
        }

        return stats

    def print_summary(self):
        """打印测试总结"""
        stats = self.analyze_sync_stability()

        if not stats:
            logger.warning("没有测试数据")
            return

        logger.info("=" * 60)
        logger.info("🔍 同步验证测试总结")
        logger.info("=" * 60)
        logger.info(f"📊 总测试段数: {stats['total_chunks']}")
        logger.info(f"🎯 频率匹配率: {stats['frequency_match_rate']:.1%}")
        logger.info(f"📈 幅值比统计:")
        logger.info(f"   均值: {stats['amplitude_ratio_mean']:.4f}")
        logger.info(f"   标准差: {stats['amplitude_ratio_std']:.6f}")
        logger.info(f"   范围: {stats['amplitude_ratio_range']:.6f}")
        logger.info(f"🔄 相位差统计:")
        logger.info(
            f"   均值: {stats['phase_diff_mean']:.3f}rad ({stats['phase_diff_mean']*180/3.14159:.1f}°)"
        )
        logger.info(f"   标准差: {stats['phase_diff_std']:.6f}rad")
        logger.info(f"   范围: {stats['phase_diff_range']:.6f}rad")

        # 评估同步质量
        if stats["amplitude_ratio_std"] < 0.01 and stats["phase_diff_std"] < 0.1:
            logger.info("✅ 同步质量评估: 优秀")
        elif stats["amplitude_ratio_std"] < 0.05 and stats["phase_diff_std"] < 0.2:
            logger.info("✅ 同步质量评估: 良好")
        else:
            logger.warning("⚠️ 同步质量评估: 需要改进")
        logger.info("=" * 60)


def test_basic_sync_verification():
    """基本同步验证测试"""
    logger.info("=== 基本同步验证测试 ===")

    # 创建测试配置
    sampling_info = init_sampling_info(20000.0, 2000)  # 20kHz, 2000样本/段
    sine_args = {"frequency": 1000.0, "amplitude": 2.0, "phase": 0.0}  # 1kHz  # 2V

    generator = SineGenerator(sampling_info, sine_args)
    tester = SyncVerificationTester()

    # 使用物理连接的通道
    ai_channel = "400Slot2/ai1"  # 连接到 AO 的 AI 通道
    ao_channel = "400Slot2/ao1"  # 输出通道

    logger.info(f"使用通道: AI={ai_channel}, AO={ao_channel}")
    logger.info("请确保这两个通道已用信号线物理连接")

    sync_io = ContSyncSineAIAO(
        ai_channel=ai_channel,
        ao_channel=ao_channel,
        waveform_generator=generator,
        export_function=tester.export_function,
    )

    try:
        # 启动任务
        logger.info("启动同步验证任务...")
        sync_io.start()

        # 稳定期
        time.sleep(2.0)

        # 第一次数据采集
        logger.info("🔄 第一次数据采集（3秒）...")
        sync_io.enable_export = True
        time.sleep(3.0)

        # 暂停采集
        logger.info("⏸️ 暂停数据采集...")
        sync_io.enable_export = False
        time.sleep(1.0)

        # 第二次数据采集
        logger.info("🔄 第二次数据采集（3秒）...")
        sync_io.enable_export = True
        time.sleep(3.0)

        # 停止任务
        sync_io.stop()

        # 分析结果
        tester.print_summary()

        return tester

    except Exception as e:
        logger.error(f"基本同步验证测试失败: {e}")
        sync_io.stop()
        raise


def test_frequency_sweep_sync():
    """频率扫描同步验证测试"""
    logger.info("=== 频率扫描同步验证测试 ===")

    # 创建测试配置
    sampling_info = init_sampling_info(20000.0, 2000)  # 20kHz, 2000样本/段
    sine_args = {"frequency": 500.0, "amplitude": 1.5, "phase": 0.0}  # 起始频率  # 1.5V

    generator = SineGenerator(sampling_info, sine_args)
    tester = SyncVerificationTester()

    ai_channel = "400Slot2/ai1"
    ao_channel = "400Slot2/ao1"

    sync_io = ContSyncSineAIAO(
        ai_channel=ai_channel,
        ao_channel=ao_channel,
        waveform_generator=generator,
        export_function=tester.export_function,
    )

    try:
        # 启动任务
        sync_io.start()
        sync_io.enable_export = True

        # 频率扫描测试
        frequencies = [500, 1000, 1500, 2000]  # Hz

        for i, freq in enumerate(frequencies):
            logger.info(f"🎵 测试频率 {i+1}/{len(frequencies)}: {freq} Hz")

            # 更新频率
            generator._next_sine_args["frequency"] = freq  # type: ignore

            # 在该频率下采集数据
            time.sleep(2.5)

        # 停止任务
        sync_io.stop()

        # 分析结果
        tester.print_summary()

        return tester

    except Exception as e:
        logger.error(f"频率扫描同步验证测试失败: {e}")
        sync_io.stop()
        raise


def test_multiple_start_stop_sync():
    """多次启动停止同步验证测试"""
    logger.info("=== 多次启动停止同步验证测试 ===")

    all_results = []

    for cycle in range(2):  # 测试2个周期
        logger.info(f"🔄 开始第 {cycle + 1}/2 个测试周期")

        # 创建新的测试配置
        sampling_info = init_sampling_info(15000.0, 1500)  # 15kHz, 1500样本/段
        sine_args = {
            "frequency": 1200.0,  # 1.2kHz
            "amplitude": 1.8,  # 1.8V
            "phase": 0.0,
        }

        generator = SineGenerator(sampling_info, sine_args)
        tester = SyncVerificationTester()

        ai_channel = "400Slot2/ai1"
        ao_channel = "400Slot2/ao1"

        sync_io = ContSyncSineAIAO(
            ai_channel=ai_channel,
            ao_channel=ao_channel,
            waveform_generator=generator,
            export_function=tester.export_function,
        )

        try:
            # 启动任务
            sync_io.start()
            time.sleep(1.0)

            # 数据采集
            sync_io.enable_export = True
            time.sleep(2.0)

            # 停止任务
            sync_io.stop()

            # 保存结果
            all_results.extend(tester.test_results)

            logger.info(
                f"第 {cycle + 1} 个周期完成，采集了 {len(tester.test_results)} 段数据"
            )

            # 短暂休息
            time.sleep(1.0)

        except Exception as e:
            logger.error(f"第 {cycle + 1} 个周期失败: {e}")
            sync_io.stop()
            continue

    # 分析所有结果
    if all_results:
        # 创建汇总测试器
        summary_tester = SyncVerificationTester()
        summary_tester.test_results = all_results
        summary_tester.print_summary()

        return summary_tester
    else:
        logger.error("没有收集到测试数据")
        return None


def main():
    """主测试函数"""
    logger.info("🚀 开始同步验证测试")
    logger.info("📋 测试计划: 基本同步 → 频率扫描 → 多次启停")
    logger.info("⚠️ 请确保 400Slot2/ai1 和 400Slot2/ao1 通道已用信号线连接")
    logger.info("⏱️ 预计总时间: ~30秒")
    logger.info("=" * 60)

    try:
        # 1. 基本同步验证
        logger.info("🔧 [1/3] 基本同步验证测试")
        basic_tester = test_basic_sync_verification()
        time.sleep(1.0)

        # 2. 频率扫描同步验证
        logger.info("🔧 [2/3] 频率扫描同步验证测试")
        sweep_tester = test_frequency_sweep_sync()
        time.sleep(1.0)

        # 3. 多次启动停止同步验证
        logger.info("🔧 [3/3] 多次启停同步验证测试")
        multi_tester = test_multiple_start_stop_sync()

        logger.info("🎉 所有同步验证测试完成！")

        return basic_tester, sweep_tester, multi_tester

    except Exception as e:
        logger.error(f"同步验证测试过程中发生错误: {e}")
        raise


if __name__ == "__main__":
    main()
